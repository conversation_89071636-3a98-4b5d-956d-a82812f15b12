import React from "react";

export default function Header() {
  return (
    <header className="flex items-center justify-between px-8 py-4 bg-white border-b">
      <div className="flex-1 max-w-lg">
        <input
          type="text"
          placeholder="Search loans, lenders..."
          className="w-full h-10 px-4 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-black"
        />
      </div>
      <div className="flex items-center gap-6 ml-8">
        <button className="relative">
          <span className="text-2xl">🔔</span>
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1">2</span>
        </button>
        <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-gray-200">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User avatar" className="w-full h-full object-cover" />
        </div>
      </div>
    </header>
  );
} 