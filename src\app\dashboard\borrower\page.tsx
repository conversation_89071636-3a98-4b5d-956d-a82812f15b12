import React from "react";

const stats = [
  { label: "Active Loans", value: 2, sub: "₦620,000 total", icon: "💳" },
  { label: "Next Payment", value: "12 Days", sub: "₦83,333 due", icon: "📅" },
  { label: "Total Repaid", value: "₦186,666", sub: "30% complete", icon: "✅" },
  { label: "Available Offers", value: 12, sub: "View Marketplace", icon: "🎁", link: "#" },
];

const offers = [
  { bank: "First Bank", amount: "₦300,000", term: "12 months @ 22% p.a.", icon: "🏦" },
  { bank: "GTBank", amount: "₦500,000", term: "18 months @ 20% p.a.", icon: "🏦" },
  { bank: "Access Bank", amount: "₦150,000", term: "6 months @ 18% p.a.", icon: "🏦" },
];

const payments = [
  {
    id: "LN-001234",
    due: "June 12, 2025",
    amount: "₦83,333",
    warning: true,
  },
  {
    id: "LN-001235",
    due: "May 28, 2025",
    amount: "₦20,000",
    warning: false,
  },
];

export default function BorrowerDashboard() {
  return (
    <div className="space-y-6">
      {/* Welcome Card */}
      <div className="flex items-center justify-between bg-white rounded-xl shadow p-6">
        <div>
          <h2 className="text-2xl font-bold mb-1">Welcome back, John!</h2>
          <p className="text-gray-600 text-sm">Your loan journey continues. 2 active loans, next payment due in 12 days.</p>
        </div>
        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User avatar" className="w-16 h-16 rounded-full border-2 border-gray-200" />
      </div>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <div key={stat.label} className="bg-white rounded-xl shadow p-4 flex flex-col items-start">
            <div className="text-2xl mb-2">{stat.icon}</div>
            <div className="text-lg font-semibold">{stat.value}</div>
            <div className="text-xs text-gray-500">{stat.sub}</div>
            {stat.link && (
              <a href={stat.link} className="text-xs text-blue-600 mt-2 hover:underline">{stat.sub}</a>
            )}
          </div>
        ))}
      </div>
      {/* Upcoming Payments */}
      <div className="bg-white rounded-xl shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Upcoming Payments</h3>
        <div className="space-y-3">
          {payments.map((p) => (
            <div
              key={p.id}
              className={`flex items-center justify-between rounded-lg p-4 ${p.warning ? "bg-yellow-100" : "bg-blue-50"}`}
            >
              <div className="flex items-center gap-3">
                <span className="text-xl">
                  {p.warning ? "\u26A0\uFE0F" : "\u25CF"}
                </span>
                <div>
                  <div className="font-medium">Loan #{p.id}</div>
                  <div className="text-xs text-gray-600">Due: {p.due}</div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="font-semibold text-lg">{p.amount}</div>
                <button className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800">Pay Now</button>
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* Additional Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mt-4">
        <div className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-indigo-600 mb-2">+</span>
          <span className="font-medium text-sm">Apply for New Loan</span>
        </div>
        <div className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-green-600 mb-2">&#128179;</span>
          <span className="font-medium text-sm">Update KYC</span>
        </div>
        <div className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-blue-600 mb-2">&#8635;</span>
          <span className="font-medium text-sm">View Loan History</span>
        </div>
        <div className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-orange-600 mb-2">&#128222;</span>
          <span className="font-medium text-sm">Contact Support</span>
        </div>
      </div>
      {/* Recommended Offers */}
      <div className="bg-white rounded-xl shadow p-6 mt-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Recommended for You</h3>
          <a href="#" className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 text-sm font-medium">Go to Marketplace</a>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {offers.map((offer) => (
            <div key={offer.bank} className="border rounded-lg p-4 flex flex-col items-start bg-gray-50">
              <div className="flex items-center mb-2">
                <span className="text-xl mr-2">{offer.icon}</span>
                <span className="font-semibold">{offer.bank}</span>
              </div>
              <div className="text-2xl font-bold mb-1">{offer.amount}</div>
              <div className="text-xs text-gray-500 mb-4">{offer.term}</div>
              <button className="w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700 text-sm font-medium">View Details</button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 